const DatabaseSeeder = require("./dataGenerator");

async function main() {
  const seeder = new DatabaseSeeder();

  // You can customize the amounts here
  const options = {
    users: 50, // Number of users to generate
    categories: 50, // Number of categories
    brands: 50, // Number of brands
    tags: 50, // Number of tags
    warehouses: 50, // Number of warehouses
    products: 50, // Number of products
    orders: 50, // Number of orders
    discounts: 50, // Number of discount codes
    reviews: 50, // Number of reviews
    newsletters: 50, // Number of newsletter subscriptions
    pageViews: 50, // Number of page views for analytics
    clearData: false, // Set to true to clear existing data first
  };

  try {
    await seeder.generateAll(options);
    console.log("✅ Seeding completed successfully!");
  } catch (error) {
    console.error("❌ Seeding failed:", error);
    process.exit(1);
  }
}

main();
